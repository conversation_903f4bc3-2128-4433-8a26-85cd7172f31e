<script setup lang="ts">
import { useEchoPublic } from '@laravel/echo-vue';
import { useIdle } from '@vueuse/core';
import { onBeforeUnmount, onMounted, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import PageLoader from '@/components/global/PageLoader.vue';
import { AndroidActions, handleAndroidEvent, requestAndroid } from '@/pages/cash-register/androidRequestHandler';
import CashierScreen from '@/pages/cash-register/CashierScreen.vue';
import CustomerScreen from '@/pages/cash-register/CustomerScreen.vue';
import CashRegisterLoginModal from '@/pages/cash-register/dialogs/CashRegisterLoginModal.vue';
import { useCashAuthStore } from '@/stores/cash-auth-store';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { CustomEvents, sendCustomEvent } from '@/util/facades/custom-event';
import { CRType, type WSEvent } from '@/util/types/api-responses';

const route = useRoute();

const authStore = useCashAuthStore();
const store = useCashRegisterStore();

const loading = ref(true);
const showLogin = ref(false);
let stopListening: () => void;

const { idle, reset } = useIdle(5 * 60 * 1000); // 5 min
watch(idle, (idleValue) => {
  if (import.meta.env.DEV) {
    return;
  }

  if (idleValue && store.type === CRType.CASHIER) {
    authStore.logout();
  }
});

watch(() => authStore.user, user => {
  if (!user) {
    showLogin.value = true;
  }
});

watch(() => store.crID, async() => {
  if (store.type === CRType.CASHIER) {
    await authStore.fetchUser();
    showLogin.value = Boolean(!authStore.user);
  }

  loading.value = false;

  const { listen, stopListening: stop } = useEchoPublic(`cash_register.${store.crID}.frontend.${store.feID}`, ['CashRegisterFrontendEvent'], async(e: WSEvent) => {
    const { action, data: { id } = {}} = e;

    sendCustomEvent({ action: action }, CustomEvents.CashRegisterFrontendEvent);

    if (action === 'order_updated' || action === 'order_created') {
      const { data: { data: order }} = await cashApi.get(`/api/cash-register/orders/${id}`);
      store.order = order;
    } else if (action === 'order_paid' || action === 'order_payment_failed') {
      store.paymentDoneID = id;
    } else if (action === 'order_refunded' || action === 'order_refund_failed') {
      // Handle refund events - could be used to update UI or show notifications
      console.log(`Refund event: ${action} for order ${id}`);
    } else if (action === 'cash_register_command') {
      const { data: { data: androidRequest }} = await cashApi.get(`/api/cash-register/commands/${id}`);
      requestAndroid(androidRequest);
    } else if (action === 'cash_register_command_response') {
      const { data } = await cashApi.get(`/api/cash-register/commands/${id}`);
      store.commandResponse = data;
    } else if (action === 'cash_register_frontend_reload') {
      location.reload();
    }
  });

  listen();
  stopListening = stop;
});

onMounted(async() => {
  const data = { action: AndroidActions.INIT } as { action: string; actualDisplay?: string, uuid?: string };
  if (import.meta.env.DEV) {
    data.actualDisplay = (route.query.ad ?? '0') as string;
    data.uuid = route.query.uuid as string;
  }

  requestAndroid(data);
  window.addEventListener('android-data', handleAndroidEvent);
});
onBeforeUnmount(() => {
  window.removeEventListener('android-data', handleAndroidEvent);
  stopListening();
});
</script>

<template>
  <main class="[&_*]:select-none">
    <PageLoader v-if="loading" absolute-center />
    <CashierScreen v-else-if="store.type === CRType.CASHIER" :key="authStore.user?.id ?? 1" />
    <CustomerScreen v-else-if="store.type === CRType.CUSTOMER" />
    <CashRegisterLoginModal v-if="showLogin" @on-success="reset" @close="showLogin = false" />
  </main>
</template>
