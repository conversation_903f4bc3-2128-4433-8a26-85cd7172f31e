<script setup lang="ts">
import { IconLoader2, IconSearch, IconCircleCheck, IconExclamationCircle, IconCreditCard } from '@tabler/icons-vue';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import Pagination from '@/components/global/Pagination.vue';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shadcn-components/ui/table';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';
import { formatDate } from '@/util/datetime';
import { CustomEvents } from '@/util/facades/custom-event';
import { getFromMultiLangObject } from '@/util/multilang';
import { deployToast, ToastType } from '@/util/toast';
import type { CROrder, Response } from '@/util/types/api-responses';
import { AndroidActions } from '../androidRequestHandler';

interface QRResponseData {
  data: string
}

const { t } = useI18n();
const emit = defineEmits(['close']);
const store = useCashRegisterStore();

const orders = ref<Response<CROrder[]>>();
const selectedOrder = ref<CROrder>();
const orderDetail = ref<CROrder>();
const searchInput = ref('');
const loading = ref(false);
const qrLoading = ref(false);
const refundLoading = ref(false);
const refundResult = ref<'PENDING' | 'SUCCESS' | 'ERROR'>('PENDING');
const errorMessage = ref('');

const fetchOrders = async({ page = 1 }: { page?: number } = {}) => {
  loading.value = true;
  try {
    const { data } = await cashApi.get<Response<CROrder[]>>('/api/cash-register/orders', {
      params: {
        page,
        limit: 10,
      },
    });
    orders.value = data;
  } catch (error) {
    console.error('Error fetching orders:', error);
    deployToast(ToastType.ERROR, {
      text: t('cash-register.error-fetching-orders'),
      timeout: 6000,
    });
  } finally {
    loading.value = false;
  }
};

const searchOrder = async() => {
  if (!searchInput.value.trim()) {
    await fetchOrders();
    return;
  }

  loading.value = true;
  try {
    const { data } = await cashApi.post<Response<CROrder[]>>(`/api/cash-register/orders/${searchInput.value}/find`);
    orders.value = data;
  } catch (error) {
    console.error('Error searching order:', error);
    deployToast(ToastType.ERROR, {
      text: t('cash-register.error-searching-order'),
      timeout: 6000,
    });
  } finally {
    loading.value = false;
  }
};

const createOnQRScanHandler = (resolve: (data: QRResponseData | undefined) => void) => {
  return (event: Event) => {
    const { detail } = event as CustomEvent<{ action: string; data: any }>;
    if (detail.action === AndroidActions.DATA_FROM_READER) {
      resolve({ data: detail.data });
    }
  };
};

const scanQR = async() => {
  qrLoading.value = true;

  try {
    await cashApi.post('/api/cash-register/commands', {
      data: {
        action: AndroidActions.DATA_FROM_READER,
      },
    });

    const { promise: qrScanPromise, resolve, reject } = Promise.withResolvers<QRResponseData | undefined>();
    const handler = createOnQRScanHandler(resolve);
    window.eventBus.addEventListener(CustomEvents.DataFromReaderEvent, handler);

    const timeoutId = setTimeout(() => {
      reject('Timeout');
    }, 15000);

    const qrData = await qrScanPromise;
    clearTimeout(timeoutId);
    window.eventBus.removeEventListener(CustomEvents.DataFromReaderEvent, handler);

    if (qrData?.data) {
      // Use the QR data to find the order
      searchInput.value = qrData.data;
      await searchOrder();
    }
  } catch (error) {
    console.error('Error scanning QR:', error);
    deployToast(ToastType.ERROR, {
      text: t('cash-register.error-scanning-qr'),
      timeout: 6000,
    });
  } finally {
    qrLoading.value = false;
  }
};

const selectOrder = async(order: CROrder) => {
  selectedOrder.value = order;

  try {
    const { data: { data }} = await cashApi.get(`/api/cash-register/orders/${order.id}`);
    orderDetail.value = data;
  } catch (error) {
    console.error('Error fetching order detail:', error);
    deployToast(ToastType.ERROR, {
      text: t('cash-register.error-fetching-order-detail'),
      timeout: 6000,
    });
  }
};

const refundOrder = async() => {
  if (!selectedOrder.value) {
    return;
  }

  refundLoading.value = true;
  refundResult.value = 'PENDING';

  try {
    await cashApi.post(`/api/cash-register/orders/${selectedOrder.value.id}/refund`, {
      data: orderDetail.value?.id, // QR content in JSON body parameter data
    });

    refundResult.value = 'SUCCESS';
    deployToast(ToastType.SUCCESS, {
      text: t('cash-register.refund-successful'),
      timeout: 6000,
    });

    // Auto close modal after 10 seconds
    setTimeout(() => {
      emit('close');
    }, 10000);
  } catch (error: any) {
    refundResult.value = 'ERROR';
    errorMessage.value = error.response?.data?.message || t('cash-register.refund-failed');
    deployToast(ToastType.ERROR, {
      text: errorMessage.value,
      timeout: 6000,
    });
  } finally {
    refundLoading.value = false;
  }
};

const goBack = () => {
  selectedOrder.value = undefined;
  orderDetail.value = undefined;
  refundResult.value = 'PENDING';
  errorMessage.value = '';
};

// Initialize
fetchOrders();
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[800px] w-full bg-white p-10 rounded-xl max-h-[90vh] overflow-y-auto">
      <div class="space-y-4">
        <h2 class="text-xl font-bold">{{ $t('cash-register.refund') }}</h2>

        <!-- Order Selection View -->
        <div v-if="!selectedOrder">
          <!-- Search and QR Scan -->
          <div class="flex gap-4 mb-4">
            <div class="flex-1 flex gap-2">
              <input
                v-model="searchInput"
                type="text"
                :placeholder="$t('cash-register.search-order-uid')"
                class="flex-1 px-3 py-2 border rounded-lg"
                @keyup.enter="searchOrder"
              >
              <button
                class="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center gap-2"
                :disabled="loading"
                @click="searchOrder"
              >
                <IconLoader2 v-if="loading" class="animate-spin h-4 w-4" />
                <IconSearch v-else class="h-4 w-4" />
                {{ $t('misc.search') }}
              </button>
            </div>
            <button
              class="px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors flex items-center gap-2"
              :disabled="qrLoading"
              @click="scanQR"
            >
              <IconLoader2 v-if="qrLoading" class="animate-spin h-4 w-4" />
              <IconCreditCard v-else class="h-4 w-4" />
              {{ $t('cash-register.scan-qr') }}
            </button>
          </div>

          <!-- Orders Table -->
          <div v-if="loading" class="flex justify-center py-8">
            <IconLoader2 class="animate-spin h-8 w-8 text-gray-400" />
          </div>

          <Table v-else-if="orders?.data.length" class="mt-4 text-base">
            <TableHeader>
              <TableRow>
                <TableHead>#</TableHead>
                <TableHead>{{ $t('cash-register.order-id') }}</TableHead>
                <TableHead>{{ $t('cash-register.items') }}</TableHead>
                <TableHead>{{ $t('cash-register.total') }}</TableHead>
                <TableHead>{{ $t('misc.status') }}</TableHead>
                <TableHead>{{ $t('misc.date') }}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <TableRow
                v-for="(order, idx) in orders.data"
                :key="order.id"
                class="cursor-pointer hover:bg-gray-50"
                @click="selectOrder(order)"
              >
                <TableCell>{{ idx + 1 }}.</TableCell>
                <TableCell class="font-mono text-sm">{{ order.id }}</TableCell>
                <TableCell>
                  <div class="border rounded-full w-fit px-3 bg-emerald-500 text-white">
                    {{ $t('cash-register.n-items', { n: order.items?.length ?? 0 }) }}
                  </div>
                </TableCell>
                <TableCell class="font-medium">{{ order.price_calculated.toFixed(2) }} €</TableCell>
                <TableCell>
                  <span
                    :class="[
                      order.status === 'PAID' ? 'bg-green-100 text-green-800' :
                      order.status === 'PENDING' ? 'bg-yellow-100 text-yellow-800' :
                      order.status === 'CANCELLED' ? 'bg-red-100 text-red-800' :
                      'bg-gray-100 text-gray-800',
                      'px-2 py-1 rounded-full text-xs'
                    ]"
                  >
                    {{ order.status }}
                  </span>
                </TableCell>
                <TableCell class="text-gray-400">{{ formatDate(order.created_at, true) }}</TableCell>
              </TableRow>
            </TableBody>
          </Table>

          <div v-else class="text-center text-gray-400 italic py-8">
            {{ $t('misc.no-results') }}
          </div>

          <Pagination v-if="orders?.meta" in-cash-register :meta="orders.meta" @new-page="fetchOrders" />
        </div>

        <!-- Order Detail View -->
        <div v-else class="space-y-4">
          <div class="flex items-center gap-4">
            <button
              class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
              @click="goBack"
            >
              ← {{ $t('misc.back') }}
            </button>
            <h3 class="text-lg font-semibold">{{ $t('cash-register.order-detail') }}</h3>
          </div>

          <div v-if="orderDetail" class="space-y-4">
            <!-- Order Info -->
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-2 gap-4">
                <div>
                  <strong>{{ $t('cash-register.order-id') }}:</strong> {{ orderDetail.id }}
                </div>
                <div>
                  <strong>{{ $t('misc.status') }}:</strong> {{ orderDetail.status }}
                </div>
                <div>
                  <strong>{{ $t('cash-register.total') }}:</strong> {{ orderDetail.price_calculated.toFixed(2) }} €
                </div>
                <div>
                  <strong>{{ $t('misc.date') }}:</strong> {{ formatDate(orderDetail.created_at, true) }}
                </div>
              </div>
            </div>

            <!-- Order Items -->
            <div>
              <h4 class="font-semibold mb-2">{{ $t('cash-register.items') }}</h4>
              <div class="space-y-2">
                <div
                  v-for="item in orderDetail.items"
                  :key="item.id"
                  class="flex justify-between items-center p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <div class="font-medium">{{ getFromMultiLangObject(item.product.name) }}</div>
                    <div class="text-sm text-gray-500">{{ $t('cash-register.quantity') }}: {{ item.quantity }}</div>
                  </div>
                  <div class="text-right">
                    <div class="font-medium">{{ item.price_calculated.toFixed(2) }} €</div>
                    <div class="text-sm text-gray-500">{{ item.price_unit.toFixed(2) }} € / {{ $t('misc.piece') }}</div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Refund Actions -->
            <div v-if="refundResult === 'PENDING'" class="pt-4 border-t">
              <button
                class="w-full bg-red-500 text-white py-3 rounded-lg font-medium hover:bg-red-600 transition-colors flex items-center justify-center gap-2"
                :disabled="refundLoading"
                @click="refundOrder"
              >
                <IconLoader2 v-if="refundLoading" class="animate-spin h-5 w-5" />
                {{ $t('cash-register.refund-order') }}
              </button>
            </div>

            <!-- Refund Result -->
            <div v-else-if="refundResult === 'SUCCESS'" class="pt-4 border-t">
              <div class="flex items-center gap-2 text-green-600 bg-green-50 p-4 rounded-lg">
                <IconCircleCheck class="h-5 w-5" />
                <span class="font-medium">{{ $t('cash-register.refund-successful') }}</span>
              </div>
              <p class="text-sm text-gray-500 mt-2">{{ $t('cash-register.modal-will-close-automatically') }}</p>
            </div>

            <div v-else-if="refundResult === 'ERROR'" class="pt-4 border-t">
              <div class="flex items-center gap-2 text-red-600 bg-red-50 p-4 rounded-lg">
                <IconExclamationCircle class="h-5 w-5" />
                <span class="font-medium">{{ errorMessage || $t('cash-register.refund-failed') }}</span>
              </div>
              <button
                class="w-full bg-blue-500 text-white py-2 rounded-lg font-medium hover:bg-blue-600 transition-colors mt-2"
                @click="refundResult = 'PENDING'"
              >
                {{ $t('misc.try-again') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
