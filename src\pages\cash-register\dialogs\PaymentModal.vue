<script setup lang="ts">
import { IconGiftCard, IconCash, IconCreditCardPay, IconArrowLeft, IconCircleCheck, IconExclamationCircle, IconRefresh } from '@tabler/icons-vue';
import { Loader2 } from 'lucide-vue-next';
import { computed, onUnmounted, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import Keypad from '@/pages/cash-register/dialogs/components/Keypad.vue';
import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi from '@/util/cashAxios';

type Method = 'CASH' | 'PAYMENT_CARD' | 'VOUCHER';

const { t } = useI18n();
const store = useCashRegisterStore();

const emit = defineEmits(['close']);

const timeout = ref<ReturnType<typeof setTimeout>>();
const paymentMethod = ref<Method | undefined>();
const payed = ref<string[]>([]);
const pending = ref(false);
const payments = ref<{ method?: Method; amount: number }[]>([]);
const remaining = ref(0);
const errorMessage = ref();
const paymentResult = ref<'OK' | 'ERROR' | 'PENDING'>('PENDING');

const pay = async() => {
  try {
    timeout.value = setTimeout(() => {
      paymentResult.value = 'ERROR';
      errorMessage.value = t('cash-register.payment-timeout');
    }, 30 * 1000);

    pending.value = true;
    await cashApi.post(`/api/cash-register/orders/${store.order!.id}/payment`, {
      payments: payments.value,
    });
    store.paymentDoneID = undefined;
  } catch (error) {
    pending.value = false;
    paymentResult.value = 'ERROR';
    errorMessage.value = 'Payment failed';
    if (timeout.value) {
      clearTimeout(timeout.value);
    }
  }
};

const payByCash = () => {
  payments.value.push({
    method: paymentMethod.value,
    amount: Number(payedFormatted.value),
  });

  pay();
};

const payByCard = () => {
  paymentMethod.value = 'PAYMENT_CARD';
  payments.value.push({
    method: 'PAYMENT_CARD',
    amount: payments.value.length ? remaining.value : store.order!.price_calculated,
  });

  pay();
};

const splitPayment = () => {
  remaining.value = (remaining.value || store.order!.price_calculated) - Number(payedFormatted.value);

  payments.value.push({
    method: paymentMethod.value,
    amount: Number(payedFormatted.value),
  });

  paymentMethod.value = undefined;
  payed.value = [];
};

const close = () => {
  if (!pending.value) {
    emit('close');
  }
};

const tryAgain = () => {
  pending.value = false;
  paymentMethod.value = undefined;
  payments.value = [];
  paymentResult.value = 'PENDING';
};

const change = computed(() => Number(payed.value.join('')) - (remaining.value || store.order!.price_calculated_rounded));
const payedFormatted = computed(() => {
  const tmp = Number(payed.value.join(''));
  return isNaN(tmp) ? '0.' : payed.value.at(-1) === '.' ? payed.value.join('') : tmp;
});
const changeFormatted = computed(() => {
  return isNaN(change.value) || change.value < 0 ? '0.00' : change.value.toFixed(2);
});

watch(() => store.paymentDoneID, async(id) => {
  if (id) {
    pending.value = false;
    const { data: { data }} = await cashApi.get(`/api/cash-register/orders/${id}`);
    paymentResult.value = data.last_cash_register_command?.state;
    errorMessage.value = data.last_cash_register_command?.messages;

    store.paymentDoneID = undefined;

    if (timeout.value) {
      clearTimeout(timeout.value);
    }
  }
});

onUnmounted(() => {
  if (timeout.value) {
    clearTimeout(timeout.value);
  }

  if (paymentResult.value === 'OK') {
    store.clearCart();
  }

  store.commandResponse = undefined;
});
</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center font-medium" @click.self="close">
    <div class="max-w-[520px] w-full bg-white p-12 rounded-xl text-xl">

      <div v-if="(!paymentMethod || paymentMethod === 'PAYMENT_CARD') && paymentResult === 'PENDING'" class="space-y-5">
        <h2 class="text-2xl font-bold text-center">{{ $t('cash-register.select-payment-method') }}</h2>
        <div class="text-center space-y-2">
          <div class="font-normal text-base text-gray-500">{{ $t('cash-register.total') }}</div>
          <div class="font-bold text-3xl">{{ remaining ? remaining.toFixed(2) : store.order!.price_calculated.toFixed(2) }} €</div>
        </div>
        <button v-if="!payments.some(payment => payment.method === 'CASH')" class="flex items-center justify-center w-full bg-green-500 py-6 gap-4 font-medium text-white rounded-xl" @click="paymentMethod = 'CASH'">
          <IconCash />
          <span>{{ $t('cash-register.pay-cash') }}</span>
        </button>
        <button class="flex items-center justify-center w-full bg-blue-500 py-6 gap-4 font-medium text-white rounded-xl" @click="payByCard">
          <Loader2 v-if="pending" class="animate-spin" />
          <IconCreditCardPay v-else />
          <span>{{ $t('cash-register.pay-card') }}</span>
        </button>
        <button v-if="!payments.some(payment => payment.method === 'VOUCHER')" class="flex items-center justify-center w-full bg-amber-500 py-6 gap-4 font-medium text-white rounded-xl" @click="paymentMethod = 'VOUCHER'">
          <IconGiftCard />
          <span>{{ $t('cash-register.pay-gift-card') }}</span>
        </button>
      </div>

      <div v-else-if="(paymentMethod === 'CASH' || paymentMethod === 'VOUCHER') && paymentResult === 'PENDING'" class="space-y-5">
        <button class="flex items-center text-gray-500 py-3 -mb-3" @click="paymentMethod = undefined">
          <IconArrowLeft size="30" />
          <span class="ml-2">{{ $t('cash-register.back') }}</span>
        </button>

        <div class="bg-slate-200 p-5 rounded-xl space-y-3">
          <div class="flex justify-between items-center">
            <div class="font-normal text-slate-500">{{ $t('cash-register.total') }}{{ store.order!.price_calculated_rounded !== store.order!.price_calculated ? ` (${ $t('cash-register.rounded') })` : '' }}:</div>
            <div class="font-bold text-2xl">{{ remaining ? remaining.toFixed(2) : store.order!.price_calculated_rounded.toFixed(2) }} €</div>
          </div>
          <div v-if="store.order!.price_calculated_rounded !== store.order!.price_calculated" class="flex justify-between !-mt-0.5 items-center opacity-55">
            <div class="font-normal text-lg text-slate-500">{{ $t('cash-register.original-price') }}:</div>
            <div class="font-bold text-xl">{{ remaining ? remaining.toFixed(2) : store.order!.price_calculated.toFixed(2) }} €</div>
          </div>
          <div class="flex justify-between items-center">
            <div class="font-normal text-slate-500">{{ $t('cash-register.payed') }}:</div>
            <div class="font-bold text-2xl">{{ payedFormatted }} €</div>
          </div>
          <div class="flex justify-between items-center">
            <div class="font-normal text-slate-500">{{ $t('cash-register.to-change') }}:</div>
            <div :class="[change >= 0 ? 'text-green-600' : 'text-red-600', 'font-bold text-2xl']">{{ changeFormatted }} €</div>
          </div>
        </div>

        <Keypad v-model="payed" with-dot />

        <button v-if="change >= 0" :disabled="pending" class="w-full bg-emerald-500 py-5 font-medium text-white rounded-xl fig justify-center" @click="payByCash">
          <Loader2 v-if="pending" class="animate-spin" />
          <span>{{ $t('cash-register.complete-payment') }}</span>
        </button>
        <button v-else-if="Number(payed.join('')) > 0" class="w-full bg-blue-500 py-5 font-medium text-white rounded-xl" @click="splitPayment">
          <span>{{ $t('cash-register.split-payment') }}</span>
        </button>
        <button v-else class="w-full bg-neutral-400 py-5 font-medium text-white rounded-xl">
          <span>{{ $t('cash-register.complete-payment') }}</span>
        </button>
      </div>

      <div v-else-if="paymentResult === 'OK'" class="space-y-5">
        <div class="flex flex-col text-emerald-500 w-fit mx-auto">
          <IconCircleCheck size="80" stroke-width="1.6" class="mx-auto" />
          <div class="font-bold text-2xl">{{ $t('cash-register.payment-successful') }}</div>
        </div>
        <div class="bg-slate-200 p-5 rounded-xl space-y-3 mt-14">
          <div class="flex justify-between items-center">
            <div class="font-normal text-slate-500">{{ $t('cash-register.payment-method') }}:</div>
            <div class="font-medium">{{ $t(`cash-register.${paymentMethod?.toLowerCase()}`) }}</div>
          </div>
          <div class="flex justify-between items-center">
            <div class="font-normal text-slate-500">{{ $t('cash-register.total') }}:</div>
            <div class="font-bold text-2xl">{{ store.order!.price_calculated.toFixed(2) }} €</div>
          </div>
          <div class="flex justify-between items-center">
            <div class="font-normal text-slate-500">{{ $t('cash-register.payed') }}:</div>
            <div class="font-bold text-2xl">{{ paymentMethod === 'CASH' ? payedFormatted : store.order!.price_calculated.toFixed(2) }} €</div>
          </div>
          <div v-if="paymentMethod === 'CASH'" class="flex justify-between items-center">
            <div class="font-normal text-slate-500">{{ $t('cash-register.change') }}:</div>
            <div class="font-bold text-2xl">{{ changeFormatted }} €</div>
          </div>
        </div>
      </div>

      <div v-else-if="paymentResult === 'ERROR'" class="space-y-5">
        <div class="flex flex-col text-red-500 w-fit mx-auto">
          <IconExclamationCircle size="80" stroke-width="1.6" class="mx-auto" />
          <div class="font-bold text-2xl">{{ $t('cash-register.payment-failed') }}</div>
        </div>

        <div class="bg-rose-50 border border-rose-200 p-5 rounded-xl space-y-3 mt-14 text-lg">
          <div v-if="Array.isArray(errorMessage)" class="space-y-2">
            <div v-for="(error, index) in errorMessage" :key="index" class="flex gap-2 items-center">
              <span class="font-medium">{{ error.message }}</span>
              <span class="text-sm text-gray-500 mt-0.5">({{ error.code }})</span>
            </div>
          </div>
          <div v-else>
            {{ errorMessage ?? $t('cash-register.payment-error') }}
          </div>
        </div>

        <div class="fig text-lg">
          <button class="w-full border py-3 font-medium rounded-xl" @click="close">
            <span>{{ $t('misc.close') }}</span>
          </button>
          <button class="w-full bg-blue-500 py-3 font-medium text-white rounded-xl fig justify-center" @click="tryAgain">
            <IconRefresh />
            <span>{{ $t('misc.try-again') }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>
