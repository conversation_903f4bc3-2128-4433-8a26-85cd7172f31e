<script setup lang="ts">
import { IconReceiptEuro, IconReport, IconReceiptRefund, IconReceipt, IconRefresh } from '@tabler/icons-vue';

const emit = defineEmits([
  'open-deposit-withdrawal',
  'open-report',
  'open-refund',
  'open-invoice-payment',
  'open-zero-receipt-reset',
  'close',
]);

const emitWrapper = (...args: Parameters<typeof emit>) => {
  emit('close');
  emit(...args);
};

</script>

<template>
  <div class="fixed inset-0 z-50 bg-black/65 grid place-items-center" @click.self="emit('close')">
    <div class="max-w-[600px] w-full bg-white p-10 rounded-xl">
      <h2 class="font-bold text-xl mb-4">{{ $t(`cash-register.operations`) }}</h2>
      <div class="grid grid-cols-3 gap-4">
        <button class="p-4 border h-full rounded-md border-amber-200 bg-amber-50 text-amber-700" @click="emitWrapper('open-deposit-withdrawal')">
          <div class="flex gap-2.5">
            <IconReceiptEuro />
            <div>{{ $t('cash-register.deposit') }}/{{ $t('cash-register.withdrawal') }}</div>
          </div>
        </button>
        <button class="p-4 border h-full rounded-md border-orange-200 bg-orange-50 text-orange-700" @click="emitWrapper('open-report')">
          <div class="flex gap-2.5">
            <IconReport />
            <div>{{ $t('cash-register.report') }}</div>
          </div>
        </button>
        <button class="p-4 border h-full rounded-md border-red-200 bg-red-50 text-red-700" @click="emitWrapper('open-refund')">
          <div class="flex gap-2.5">
            <IconReceiptRefund />
            <div>{{ $t('cash-register.refund') }}</div>
          </div>
        </button>
        <button class="p-4 border h-full rounded-md border-blue-200 bg-blue-50 text-blue-700" @click="emitWrapper('open-invoice-payment')">
          <div class="flex gap-2.5">
            <IconReceipt />
            <div>{{ $t('cash-register.invoice-payment') }}</div>
          </div>
        </button>
        <button class="p-4 border h-full rounded-md border-gray-200 bg-gray-50 text-gray-700" @click="emitWrapper('open-zero-receipt-reset')">
          <div class="flex gap-2.5">
            <IconRefresh />
            <div>{{ $t('cash-register.zero-receipt-reset') }}</div>
          </div>
        </button>
      </div>
    </div>
  </div>
</template>
