import type { UserData } from '@/stores/auth-store';
import type { UserData as CashUserData } from '@/stores/cash-auth-store';
import type { PermissionData, RoleData } from '@/util/types/roles-and-permissions';

export type OAuthTokenResponse = {
  token_type: string,
  expires_in: number,
  access_token: string,
  refresh_token: string,
}

export type OAuthUserResponse = {
  data: UserData
}

export type OAuthCashUserResponse = {
  data: CashUserData
}

export type Link = {
  first: string;
  last: string;
  prev: string | null;
  next: string | null;
}

export type ApiUsersResponse = {
  data: UserData[];
  links: Link;
  meta: {
    current_page: number;
    from: number;
    last_page: number;
    links: Link[];
    path: string;
    per_page: number;
    to: number;
    total: number;
  };
}

export type ApiRolesResponse = {
  data: RoleData[];
}

export type ApiPermissionsResponse = {
  data: PermissionData[];
}

export type Response<T> = {
  data: T;
  links: Link;
  meta: Meta
}

export interface Meta {
  current_page: number;
  from: number;
  last_page: number;
  links: Link[];
  path: string;
  per_page: number;
  to: number;
  total: number;
}

export interface MultiLang {
  en: string;
  sk: string;
}

export interface Attachment {
  id?: number;
  type_id?: string | null;
  type?: {
    id: string;
    name: MultiLang;
  }
  note?: string;
  created_at?: string;
  updated_at?: string;
  items: Array<{
    id?: number;
    filename: string;
    url?: string;
    url_thumb: string;
    file_original_name?: string;
    size?: string;
    mime_type?: string;
    created_at?: string;
    updated_at?: string;
  }>;
}

export interface Product {
  id: string;
  type: 'product' | 'service' | 'deposit_packaging' | 'ticket';
  image?: string;
  cover_image_thumb?: string;
  cover_image_url?: string;
  supplier_name: string | undefined;
  name: MultiLang;
  description: MultiLang;
  active: boolean;
  code: string;
  price: number;
  price_unit: number;
  plu: number;
  ean: string;
  tax_id: number;
  tax?: Tax;
  ticket?: {
    time_ticket: boolean;
    ticket_time: number;
    additional_time: number;
    one_way_ticket: boolean;
    scan_type: number;
    turnstile_ticket_categories: Array<{
      id: string;
      name: string;
      zone_level: number;
    }>;
  };
  unit_id: number;
  unit?: Unit;
  created?: string;
  updated?: string;
  categories: string[] | Category[];
}

export interface Unit {
  id: string
  name: MultiLang;
  shortcut: MultiLang;
}

export interface AttachmentType {
  id: string
  name: MultiLang;
}

export interface Tax {
  id: string
  name: string
  code: string
  rate: number
}

export interface Category {
  id: string
  name: MultiLang
  color: string
  parent_id: string | null
}

export type Tree = {
  id: string;
  label: MultiLang;
  color?: string;
  children: Tree[];
}[]

export interface ProductFamily {
  id: string;
  name: MultiLang;
  attachments: Attachment[];
}

export interface ProductEmanStock {
  cn?: string;
  warehouses?: ProductEmanStockWarehouse[];
  employees?: ProductEmanStockEmployee[];
}

export interface ProductEmanStockWarehouse {
  name: string;
  stocked: number;
  count: number;
}

export interface ProductEmanStockEmployee {
  name: string;
  count: number;
  receipt_date?: string;
  return_data?: string;
}

export enum CRType {
  CASHIER = 'CASHIER',
  CUSTOMER = 'CUSTOMER',
}

export interface CRFrontend {
  id: string,
  type: CRType,
  display: string,
  resolution: {
    width: number,
    height: number
  }
  created_at: string
}

export interface CR {
  active: boolean
  auto_logout?: number
  created_at: string
  product_image?: boolean
  cash_register_groups?: string[] | CRGroup[]
  associated_cash_registers?: ACR[]
  device_id: string
  frontends: CRFrontend[]
  payment_terminal: boolean
  printer: boolean
  id: string
  ip_address: string
  layout: CRLayout
  name: string
  updated_at: string
}

export interface CRComponent {
  type: string
  x: number
  y: number
  w: number
  h: number
}

export interface CRLayout {
  type?: CRType;
  components?: CRComponent[];
}

export interface CROrderItem {
  id: string;
  name: string;
  type: string;
  price: number;
  price_unit: number;
  price_calculated: number;
  product: Product
  discount_calculated: number;
  discount_amount?: number;
  discount_type?: 'PERCENT' | 'AMOUNT';
  quantity: number;
  tickets_count: number;
  unit: string;
  vat_rate: number;
  vat_rate_code: string;
  created_at: string;
  updated_at: string;
}

export interface CROrder {
  id: string;
  items: CROrderItem[];
  customer?: Customer;
  discount_type?: 'PERCENT' | 'AMOUNT';
  discount_amount?: number;
  discount_calculated: number;
  discount_calculated_order: number;
  discount_calculated_order_items: number;
  discount_calculated_order_and_customer: number;
  price: number;
  price_calculated: number
  price_calculated_rounded: number;
  status: 'PENDING' | 'PAID' | 'CANCELLED' | 'ERROR';
  created_at: string;
  updated_at: string;
}

export interface WSEvent {
  action: 'order_paid' | 'order_created' | 'order_updated' | 'order_payment' | 'order_payment_failed' | 'order_refunded' | 'order_refund_failed' | 'cash_register_command_response' | 'cash_register_command' | 'cash_register_frontend_reload';
  data: {
    id: string
  }
}
export interface CommandResponse {
  data?: {
    action: string;
    id: string;
  };
  id?: string;
  messages: Array<{
    message: string;
    code: string;
  }>;
  state: 'OK' | 'ERROR' | 'PENDING';
}

export interface CRGroup {
  id: string
  name: string;
  description: string;
  cash_registers: CR[]
  categories: Category[]
}

export interface CRTransaction {
  id: number;
  amount: number;
  created_at: string;
  currency: string;
  method: 'CASH' | 'VOUCHER';
  type: 'DEPOSIT' | 'WITHDRAWAL';
}

export interface SystemMode {
  mode: 'OPENING_HOURS' | 'EVENT';
  turnstile_display_text: string;
  opening_hours: Array<{
    shortcut: string;
    start: string;
    end: string;
  }>;
}

export interface Turnstile {
  connect: string;
  created_at: string;
  gateway: string;
  id: string;
  ip_address: string;
  mac_address: string;
  mask: string;
  port: number;
  reader_status: string;
  sn: string;
  used: boolean;
  default_categories: TurnstileCategory[],
  default_category_id_out: TurnstileCategory,
  settings?: {
    config_valid?: boolean;
    readers_flip: boolean;
    reverse: boolean;
    readers_in: {
      rfid: boolean;
      mifare: boolean;
      qr: boolean;
    };
    readers_out: {
      rfid: boolean;
      mifare: boolean;
      qr: boolean;
    };
  };
}

export interface TurnstileCategory {
  id: string;
  name: string;
  zone_level: number;
}

export type ACR = Pick<CR, 'id' | 'device_id' | 'name'> & {
  frontend: boolean;
  printer: boolean;
  payment_terminal: boolean;
};

export interface Customer {
  id: string;
  name: string;
  address: string;
  zipcode: string;
  city: string;
  email: string;
  phone: string;
  active: boolean;
  discount_percent: number;
  can_invoice_order: boolean;
  customer_groups: CustomerGroup[];
  discount_percent_calculated: number;
  cards: CustomerCard[];
  created_at: string;
}

export interface CustomerGroup {
  id: string;
  name: string;
  description: string;
  active: boolean;
  discount_percent: number;
  can_invoice_order: boolean;
  created_at: string;
}

export interface CustomerCard {
  id?: string;
  card_number: string;
  card_type: string;
  active: boolean;
}
