import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { configureEcho } from '@laravel/echo-vue';
import { createHead } from '@unhead/vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import { createApp, defineAsyncComponent } from 'vue';
import Toast from 'vue-toastification';
import { AndroidActions } from '@/pages/cash-register/androidRequestHandler';
import { loadLayoutMiddleware } from '@/router/middleware/layout-middleware';
import icons from '@/setup/import/icons';
import { TokenService, useAuthStore } from '@/stores/auth-store';
import { TokenService as CashTokenService } from '@/stores/cash-auth-store';
import type { PluginOptions } from 'vue-toastification';
import 'dayjs/locale/zh-cn';
import '@/assets/fonts/fonts.css';
import 'gridstack/dist/gridstack.min.css';

window.eventBus = new EventTarget();

if (import.meta.env.DEV && import.meta.env.VITE_DEV_DEVICE_SIMULATOR) {
  window.JSBridge = {
    sendRequest(json) {
      console.log('SIMULATOR RQ ' + json);
      const data = JSON.parse(json);
      let response = '{"origin":"SIMULATOR","response":null,"error":{"id":0,"description":"Unknown action"}}';
      if (data.action === AndroidActions.INIT) {
        response = `{"origin":"SIMULATOR","action":"${data.action}","actualDisplay":${data.actualDisplay ?? 0},"displays":[{"id":0,"resolution":{"height":1024,"width":1920}},{"id":1,"resolution":{"height":1080,"width":1920}}],"uuid":"${data.uuid ?? import.meta.env.VITE_DEV_DEVICE_UUID}","versionApp":"1.0.0","device":{"model":"SIMULATOR"}}`;
      } else if (data.action === AndroidActions.PAYMENT) {
        let hasVoucher = false;
        data.payments?.forEach((payment) => {
          if (payment.paymentTypeIndex === 3) {
            hasVoucher = true;
          }
        });
        if (hasVoucher) {
          response = `{"origin":"SIMULATOR","action":"${data.action}","response":null,"responseHex":["0x01","0x07"],"id":"${data.id ?? ''}","error":{"id":7,"typeDevice":"CASHREGISTER","description":"Je pot\u0159eba ud\u011blat Z report"}}`;
        } else {
          response = `{"origin":"SIMULATOR","action":"${data.action}","response":null,"responseHex":["0x06"],"id":"${data.id ?? ''}","response": "276\\n19\\n20250812\\n117\\nbd762456-c9ae99c9-17a50828-ed1c5980-3509a50a\\nO-B83A96C9CEA14C4ABA96C9CEA11-TEST"}`;
        }
      } else if (data.action === AndroidActions.X_REPORT || data.action === AndroidActions.Z_REPORT) {
        response = `{"origin":"SIMULATOR","action":"${data.action}","id":"${data.id ?? ''}","response":"#*010 c4d59013-0f02-3dcd-b7f6-6bad9a5af71f[BITMAP]\\n\\nO.C.a.F.A. STANDARD_ICDPH_NOICO\\nMiletiova22 / 4\\n99999 Bratislava\\nPREVDZKA: nepovinn nzov predajne,Hospodrska 980 / 21, 22222 Nitra\\n\\nKP: \\t \\t \\t \\t \\t88812345678900001\\nIO: \\t \\t \\t \\t \\t \\nI DPH: \\t \\t \\t \\t \\tSK9999999999\\nDI: \\t \\t \\t \\t \\t 1234567890\\nDtum a as:\\t \\t \\t \\t\\t 29.07.2025 16:41:47\\n-----------------------------------------------------------------------------------\\nPREHADOV UZVIERKATLAIARNE\\nZ trieb prijatch\\nOd\\t \\t \\t \\t \\t 28.07.2025\\nDo\\t \\t \\t\\t \\t 28.07.2025\\nUZVIERKA V EUR\\nVytvoren\\t \\t \\t \\t \\t 29.07.202516:41:46\\n-----------------------------------------------------------------------------------\\nDPH A\\t \\t \\t \\t \\t 23.0 %\\n OBRAT\\t\\t \\t \\t \\t 51.00\\n ODPOVEDAJCA DA\\t \\t \\t \\t \\t 9.53\\nZKLAD DANE\\t \\t \\t \\t \\t 41.47\\nDPH B\\t \\t \\t \\t \\t 5.0 %\\nOBRAT\\t \\t \\t \\t \\t 241.29\\n ODPOVEDAJCA DA\\t \\t \\t \\t \\t11.52\\n ZKLAD DANE\\t \\t \\t \\t \\t 229.77\\nDPH C\\t \\t \\t \\t \\t 0.0%\\n OBRAT\\t \\t \\t \\t \\t 0.00\\n ODPOVEDAJCA DA\\t \\t \\t \\t \\t0.00\\n ZKLAD DANE\\t \\t \\t \\t \\t 0.00\\nDPH D\\t \\t \\t \\t \\t 19.0%\\n OBRAT\\t \\t \\t \\t \\t 0.00\\n ODPOVEDAJCA DA\\t \\t \\t \\t \\t0.00\\n ZKLAD DANE\\t \\t \\t \\t \\t 0.00\\nDPH E\\t \\t \\t \\t \\t BEZDPH\\n OBRAT\\t \\t \\t \\t \\t 0.00\\n-----------------------------------------------------------------------------------\\nCELKOV OBRAT\\t\\t \\t \\t \\t 292.29\\nCELKOV DA\\t \\t \\t \\t \\t 21.05\\nCELKOV ZKLADDANE\\t \\t \\t \\t \\t 271.24\\n-----------------------------------------------------------------------------------\\nPOTADL\\nPOET POKLADNINCHDOKLADOV\\t \\t \\t \\t \\t 13\\n-----------------------------------------------------------------------------------\\nFINANN OPERCIE\\nVKLAD\\t\\t \\t \\t \\t 0.00\\nVBER\\t \\t \\t \\t \\t 0.00\\nHRADA FAKTR / DOKLADOV\\t\\t \\t \\t \\t 0.00\\nSTORNO FAKTR / DOKLADOV\\t \\t \\t \\t \\t 0.00\\n-----------------------------------------------------------------------------------\\nDRUH PLATBY\\nHOTOVOS\\t \\t \\t \\t \\t 798.05\\nPLATOBNKARTY\\t \\t \\t \\t \\t 0.00\\nPOUKZKY\\t \\t \\t \\t \\t 0.00\\nZAOKRHLENIE\\t\\t \\t \\t \\t -0.09\\n-----------------------------------------------------------------------------------\\nZSUVKA\\nHOTOVOS\\t \\t \\t \\t \\t798.05\\n-----------------------------------------------------------------------------------\\n*** FiskalPRO eKasa A verzia 1.3 ***\\n---Kd pokladnice 88812345678900001 ---\\n\\n*#010\\n","responseHex":["0x23","0x2A","0x30"]}`;
      } else if (data.action === AndroidActions.DEPOSIT || data.action === AndroidActions.WITHDRAWAL) {
        response = `{"origin":"SIMULATOR","action":"${data.action}","id":"${data.id ?? ''}","response":"","responseHex":["0x06"]}`;
      } else if (data.action === AndroidActions.DATA_FROM_READER) {
        // response = `{"origin":"SIMULATOR","action":"${data.action}","id":"${data.id ?? ''}","response":"","responseHex":["0x01","0x07"], "error":{"id":7,"typeDevice":"CASHREGISTER","description":"Je pot\u0159eba ud\u011blat Z report"}}`;
        response = `{"origin":"SIMULATOR","action":"${data.action}","id":"${data.id ?? ''}","data":"3666145210","responseHex":["0x06"]}`;
      }
      console.log('SIMULATOR RS ' + response);
      setTimeout(() => {
        window.receiveResponse(response);
      }, 500);
    },
  };
}

window.callAndroid = json => window.JSBridge.sendRequest(json);
window.fetchAndroidData = () => {
  return new Promise(resolve => {
    window.receiveResponse = (jsonString: string) => {
      try {
        resolve(JSON.parse(jsonString));
      } catch {
        resolve(jsonString);
      }
    };
  });
};

icons.addIconsToLibrary();

const options: PluginOptions = {
  transition: 'Vue-Toastification__custom',
  maxToasts: 7,
  newestOnTop: true,
};

const head = createHead();

configureEcho({
  broadcaster: 'reverb',
  key: import.meta.env.VITE_REVERB_APP_KEY,
  wsHost: import.meta.env.VITE_REVERB_HOST ?? '127.0.0.1',
  wsPort: import.meta.env.VITE_REVERB_PORT,
  wssPort: import.meta.env.VITE_REVERB_PORT,
  forceTLS: (import.meta.env.VITE_REVERB_SCHEME ?? 'https') === 'https',
  enabledTransports: ['ws', 'wss'],
  disableStats: true,
  debug: false,
  authEndpoint: import.meta.env.VITE_APP_API_URL + '/broadcasting/auth',
  bearerToken: CashTokenService.getAccessToken(),
});

const createAndMountApp = async() => {
  import('@mdi/font/css/materialdesignicons.css');
  import('@/assets/css/vue-toastification/_toastification.scss');
  await import('@/App.vue');
  const App = defineAsyncComponent(() => import('@/App.vue'));

  const piniaStore = await import('@/pinia');
  const app = createApp(App).use(piniaStore.default);
  if (TokenService.getAccessToken()) {
    await useAuthStore()
      .fetchUser();
  }
  const router = await import('./router');
  router.default.beforeEach(async(to, from, next) => {
    await loadLayoutMiddleware(to, from, next, app);
    next();
  });
  const i18n = await import('@/i18n');

  app.directive('urlify', {
    mounted(el) {
      const urlPattern = /(https?:\/\/[^\s]+)/g;
      el.innerHTML = el.innerHTML.replace(urlPattern, '<a href="$&" target="_blank" class="text-blue-500 underline">$&</a>');
    },
  });

  app.use(router.default)
    .component('font-awesome-icon', FontAwesomeIcon)
    .use(i18n.default)
    .use(Toast, options)
    .use(ElementPlus)
    .provide('i18n_instance', i18n.default)
    .use(head);

  app.mount('#app');
  import('@/assets/styles.scss');

  return app;
};

export default createAndMountApp()
  .catch(() => console.error('Failed to init vue app, check backend server.'));
