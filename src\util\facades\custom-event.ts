// This is a global facade to send custom events from parent to children / globally and be able to listen to them

export enum CustomEvents {
  CashRegisterFrontendEvent = 'CashRegisterFrontendEvent',
  DataFromReaderEvent = 'DataFromReaderEvent',
}

export const sendCustomEvent = (payload: Record<string, string>, name: CustomEvents) => {
  const event = new CustomEvent(name, { detail: payload });
  window.eventBus.dispatchEvent(event);
};
