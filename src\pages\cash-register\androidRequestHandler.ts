import { useCashRegisterStore } from '@/stores/cash-register';
import cashApi, { setDeviceIdHeader } from '@/util/cashAxios';
import { CustomEvents, sendCustomEvent } from '@/util/facades/custom-event';

export const htmlLog = (...msg: unknown[]) => {
  const log = document.createElement('div');
  log.innerText = msg.join(', ');
  document.getElementById('log')?.appendChild(log);
};

export enum AndroidActions {
  INIT = 'deviceSettings',
  PAYMENT = 'printFiscalReceipt',
  DATA_FROM_READER = 'dataFromReader',
  Z_REPORT = 'zReport',
  X_REPORT = 'xReport',
  DEPOSIT = 'cashDeposit',
  WITHDRAWAL = 'cashWithdrawal',
  GET_CARD_UID = 'getCardUID',
}

export const requestAndroid = (data: Record<string, string>) => {
  window.JSBridge?.sendRequest(JSON.stringify(data));
};

export const handleAndroidEvent = async(e: Event) => {
  const store = useCashRegisterStore();
  const { action, ...rest } = (e as CustomEvent).detail;

  if (action === AndroidActions.INIT) {
    const { actualDisplay, displays, device, inputDevices, uuid } = rest;
    setDeviceIdHeader(uuid);
    const { data: { data }} = await cashApi.post('/api/cash-register/init', {
      frontends: displays.map(({ id, resolution }) => ({ display: id.toString(), resolution })),
      meta: { device, inputDevices },
    });
    store.init(actualDisplay, data);
  } else if (action === AndroidActions.DATA_FROM_READER) {
    sendCustomEvent({ action: action, data: rest.data }, CustomEvents.DataFromReaderEvent);

    if (store.unchipTicketID) {
      await cashApi.delete(`/api/cash-register/orders/items/${store.unchipTicketID}/ticket/${rest.data}`);
      await store.loadCart();
      store.unchipTicketID = undefined;
      return;
    }

    store.dataFromReader = rest.data;
  } else {
    await cashApi.put(`/api/cash-register/commands/${rest.id}`, {
      response: rest,
    });
  }
};
